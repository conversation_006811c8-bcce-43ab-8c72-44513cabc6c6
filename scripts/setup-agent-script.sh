#!/bin/bash
set -e

### This script is used to setup the development environment for AI agents working in FlyFit ##
echo "🚀 Setting up FlyFit development environment..."

# Install Node.js 22 if not already installed
if ! command -v node &> /dev/null || [[ $(node -v) != v22* ]]; then
  echo "Installing Node.js 22..."
  # For Ubuntu/Debian
  if command -v apt-get &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
    sudo apt-get install -y nodejs
  # For macOS (using Homebrew)
  elif command -v brew &> /dev/null; then
    brew install node@22
    echo 'export PATH="/usr/local/opt/node@22/bin:$PATH"' >> ~/.bashrc
    export PATH="/usr/local/opt/node@22/bin:$PATH"
  else
    echo "⚠️ Unsupported OS. Please install Node.js 22 manually."
    exit 1
  fi
fi

# Verify Node.js and npm versions
echo "Node.js version: $(node -v)"
echo "npm version: $(npm -v)"

# Check if we're in the project root (package.json exists)
if [ ! -f "package.json" ]; then
  echo "⚠️ package.json not found. Make sure you're in the project directory."
  exit 1
fi

# Install all dependencies using the project's install:all script
echo "📦 Installing all dependencies..."
npm run install:all

echo "✅ Setup complete!"
echo "You can now run any of the npm scripts defined in package.json"
