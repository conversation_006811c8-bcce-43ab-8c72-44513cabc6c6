global.window = {};
global.window.matchMedia ||=
  jest.fn(() => ({
    matches: false,
    addListener: jest.fn(), // Deprecated but used in some libraries
    removeListener: jest.fn(),
  }));

require('react-native-gesture-handler/jestSetup');
require('react-native-reanimated').setUpTests();

jest.setTimeout(30_000);

jest.mock('expo-linking', () => ({
  ...jest.requireActual('expo-linking'),
  createURL: jest.fn(),
}));

jest.mock('expo-clipboard', () => ({
  setString: jest.fn(),
}));

jest.mock('expo-notifications', () => ({setNotificationHandler: jest.fn()}));
jest.mock('expo-modules-core', () => ({}));
jest.mock('expo-network', () => ({}));
jest.mock('expo-device', () => ({}));
jest.mock('expo-image', () => ({}));
jest.mock('expo-haptics', () => ({}));
jest.mock('expo-updates', () => ({}));
jest.mock('expo-localization', () => ({}));
jest.mock('expo-image-picker', () => ({}));
jest.mock('expo', () => ({}));
jest.mock('expo-background-fetch', () => ({}));
jest.mock('expo-task-manager', () => ({
  TaskManager: {
    defineTask: jest.fn(),
  },
  defineTask: jest.fn(),
}));
jest.mock('expo-constants', () => ({AppOwnership: 'expo', ExecutionEnvironment: 'bare'}));
jest.mock('firebase/auth', () => ({getReactNativePersistence: jest.fn(), AuthErrorCodes: {}}));

jest.mock('react-native-safe-area-context', () =>
  require('react-native-safe-area-context/jest/mock'),
);

jest.mock('react-native-context-menu-view', () => ({}));

jest.mock('react-native-webview', () => ({}));

jest.mock('react-native-paper-dates', () => ({
  registerTranslation: jest.fn(),
}));

jest.mock('@react-native-firebase/app', () => ({}));

jest.mock('@react-native-firebase/auth', () => ({}));

// Mock console.error and console.log to avoid polluting test output
jest.spyOn(console, 'error');
jest.spyOn(console, 'warn');
jest.spyOn(console, 'log');
console.error.mockImplementation(() => {});
console.warn.mockImplementation(() => {});
console.log.mockImplementation(() => {});
