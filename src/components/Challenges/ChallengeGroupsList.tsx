import {useId, useMemo} from 'react';
import {LoadingIndicator, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useChallengeChildrenGroups, useChallengeTopLevelGroups} from '@contexts';
import {OnScrollComponent, useLimitArray} from '@hooks';
import type {ChallengeGroupDocument, ChallengeStage, GroupsChallenge} from '@types';
import {getRankedGroups, isEmptyArray, isNonEmptyArray} from '@utils';
import {ChallengeViewGroupCard} from './ChallengeViewGroupCard';

type ChallengeGroupsListProps = {
  challenge: GroupsChallenge;
  isDisableEmptyList?: boolean | undefined;
  isShowTopLevel?: boolean | undefined;
  selectedParentGroup: ChallengeGroupDocument | undefined;
  selectedStage: ChallengeStage | undefined;
};

export const ChallengeGroupsList: React.FC<ChallengeGroupsListProps> = ({
  challenge,
  isDisableEmptyList,
  isShowTopLevel,
  selectedParentGroup,
  selectedStage,
}) => {
  const id = useId();
  const {data: childrenGroups, isPending} = useChallengeChildrenGroups(
    challenge.id,
    selectedParentGroup,
    isShowTopLevel,
  );
  const {data: topLevelGroups} = useChallengeTopLevelGroups(challenge.id, !isShowTopLevel);
  const groupsToDisplay = isShowTopLevel && !selectedParentGroup ? topLevelGroups : childrenGroups;

  const data = useMemo(
    () => getRankedGroups(challenge, groupsToDisplay, selectedStage),
    [challenge, groupsToDisplay, selectedStage],
  );
  const {handleLoadMore, items: limitedData} = useLimitArray(data);

  return (
    <>
      {isPending && !isDisableEmptyList && <LoadingIndicator />}
      {!isPending && isEmptyArray(limitedData) && (
        <Text pl={2}>{CONTENT_CODES().CHALLENGE.VIEW.GROUPS.NO_GROUPS_IN_LIST}</Text>
      )}

      {isNonEmptyArray(limitedData) &&
        limitedData.map(item => (
          <ChallengeViewGroupCard
            key={`${id}-${item.id}-${item.rank}`}
            challenge={challenge}
            group={item}
            rank={item.rank}
            selectedStage={selectedStage}
          />
        ))}

      <OnScrollComponent onEndReached={handleLoadMore} />
    </>
  );
};
