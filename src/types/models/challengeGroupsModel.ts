import type {RawTimestamp} from '../firebase';
import type {XOR} from '../utils';
import type {
  ChallengeBase,
  ChallengeGroupingType,
  ChallengeParticipantBase,
  DailyDataValueMapping,
  DailyDataValues,
} from './challengeBase';
import type {IsoDate, UUIDString, UUIDStringDoc} from './shared';

type ChallengeGroupAggregationConfigRealTime = {
  mode: 'REAL_TIME';
};

export type ChallengeGroupAggregationConfigBatch = {
  mode: 'BATCHED';
  nextAggregationDateTime: RawTimestamp | null;
  batchIntervalMinutes: number; // How frequently (in minutes) to run the batched aggregation
};

export type ChallengeGroupAggregationConfig = XOR<
  ChallengeGroupAggregationConfigRealTime,
  ChallengeGroupAggregationConfigBatch
>;

export const isBatchAggregationConfig = (
  config: ChallengeGroupAggregationConfig,
): config is ChallengeGroupAggregationConfigBatch => config.mode === 'BATCHED';

export const isRealTimeAggregationConfig = (
  config: ChallengeGroupAggregationConfig,
): config is ChallengeGroupAggregationConfigRealTime => config.mode === 'REAL_TIME';

/**
 * Metadata for each group level.
 * This defines the ordering and characteristics of each level in the hierarchy.
 */
export type GroupLevelMetadata = {
  level: number; // The level order (1 = top/root, increasing for lower levels)
  label: string; // e.g., "Army Component", "Command", "Corps", "Division", "Team", etc.
  groupLabel: string; // e.g. The prefix to use when creating a group under this level
  isFeedEnabled: boolean; // Whether posts/feeds are enabled at this level
  description?: string; // Optional description of the level
  /**
   * Group level aggregation configuration. For example, a trainer can set the default mode
   * to REAL_TIME for lower levels, or BATCHED for higher ones.
   */
  aggregationConfig: ChallengeGroupAggregationConfig;
};

export type GroupsChallenge = ChallengeBase & {
  groupingType: ChallengeGroupingType.GROUPS;
  /**
   * The root group ID for the groups structure.
   * All groups (teams, directorates, etc.) branch from this group.
   */
  rootGroupId: UUIDString;
  /**
   * Metadata defining each level in the hierarchy.
   * The order of items in the array determines the hierarchy levels.
   */
  groupLevels: GroupLevelMetadata[];
};

/**
 * Group document for any level in the groups challenge hierarchy.
 * Each group (Team, Directorate, Base, etc.) is stored as its own document.
 */
export type ChallengeGroupDocument = UUIDStringDoc & {
  challengeId: UUIDString; // Reference back to the parent ChallengeGroupsDocument
  name: string; // E.g., "Team Alpha", "Directorate Bravo", etc.
  parentName?: string; // E.g., "Directorate Bravo", "Base Charlie", etc.
  level: number; // Depth of the group in the hierarchy, Root = 1, Child = 2, etc.
  /**
   * If present, indicates the parent group.
   * This enables arbitrarily deep hierarchies.
   */
  parentGroupId: UUIDString | null;
  groupIds: UUIDString[];
  /**
   * Daily data aggregates, keyed by date in "yyyy-MM-dd" format.
   */
  dailyData?: DailyDataValueMapping;
  /**
   * Group aggregation configuration. De-normalized from group levels metadata.
   */
  aggregationConfig: ChallengeGroupAggregationConfig;
  isPublicGroup?: true;
};

export type ChallengeGroupDocumentRealTime<T extends ChallengeGroupDocument> = Omit<
  T,
  'aggregationConfig'
> & {
  aggregationConfig: ChallengeGroupAggregationConfigRealTime;
};

export type ChallengeGroupDocumentBatch<T extends ChallengeGroupDocument> = Omit<
  T,
  'aggregationConfig'
> & {
  aggregationConfig: ChallengeGroupAggregationConfigBatch;
};

export type ChallengeGroupDocumentWithRankingValues = ChallengeGroupDocument & {
  rank: number;
};

export type ChallengeGroupAggregateValues = Required<Pick<ChallengeGroupDocument, 'dailyData'>>;
export type ChallengeGroupAggregateValuesOptional = Pick<ChallengeGroupDocument, 'dailyData'>;

/**
 * Individual participant document for the challenge.
 * Each participant’s raw sync data and computed aggregates are stored here.
 */
export type ChallengeGroupParticipantDocument = ChallengeParticipantBase & {
  challengeId: UUIDString; // Reference to the parent ChallengeGroupDocument
  /**
   * Aggregated daily data, keyed by "yyyy-MM-dd".
   * This enables historical lookups and retroactive corrections.
   */
  dailyData?: DailyDataValueMapping;
  /**
   * An ordered array of group IDs (as strings) that the participant belongs to.
   * The order reflects the hierarchy from top-level to the leaf-level group.
   */
  groupIds: UUIDString[];
};

export type ChallengeGroupParticipantWithRankingValues = ChallengeGroupParticipantDocument &
  DailyDataValues & {
    rank: number;
  };

export type ChallengeParticipantAggregateValues = ChallengeGroupAggregateValues &
  Pick<
    ChallengeGroupParticipantDocument,
    'lastLoggedDateTime' | 'lastLoggedDistanceMeters' | 'lastLoggedSteps'
  >;

export type ChallengeGroupDocumentMetadata = {
  id: 'default';
  lastMilestoneMultipleNotificationsSent?: number;
  dailyGoalMetNotificationsSent?: IsoDate[];
};
