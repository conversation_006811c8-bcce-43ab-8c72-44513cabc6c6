import type {TimeZones} from '../dates';
import type {DeviceInfoType} from '../expo';
import type {PushNotificationTypes} from '../pushNotifications';
import type {TrackingDeviceTypes} from '../trackingDeviceTypes';
import type {ChallengeJoinInviteCode} from './inviteCodeModel';
import type {
  FullIsoDateString,
  ImageUrl,
  IsoDate,
  PushNotificationConfig,
  UUIDString,
} from './shared';
import type {WellnessBlogCategories} from './wellnessBlogModel';
import {type RawTimestamp} from '../firebase';

export enum UserType {
  TEACHER = 'TEACHER',
  CLIENT = 'CLIENT',
  STUDENT = 'STUDENT',
  TRAINER = 'TRAINER',
  COACH = 'COACH',
  ADMIN = 'ADMIN',
}

export const allDisplayUserTypes = Object.values(UserType);
export const trainerCreateUserTypes = [UserType.CLIENT, UserType.TEACHER, UserType.STUDENT];

export type HealthStatsGeneric<D> = {
  startDate: D;
  endDate: D;
  distanceMeters?: number | undefined;
  stepsCount?: number | undefined;
  exerciseMinutes?: number | undefined;
  isOverrideEntry?: boolean | undefined;
  isDistanceGpsSourced?: true;
};

export type HealthStats = HealthStatsGeneric<RawTimestamp>;

export type HealthStatsDates = HealthStatsGeneric<Date>;

export type HealthStatsFullIsoDateString = HealthStatsGeneric<FullIsoDateString>;

export type HealthStatsWithEmail = HealthStats & {
  email: string;
};

export type HealthStatsDTO = HealthStatsGeneric<string> & {
  email: string;
};

export type ImportHealthStatsBody = {
  healthStats: HealthStatsDTO[];
};

export type WeightSample = {
  weightInPounds: number;
  dateTime: RawTimestamp;
  isUserEntered?: true;
};

export type WeightSampleEdit = {
  weightInPounds: string;
  dateTime: Date;
  isUserEntered?: true;
};

export type AdminUpdateUserRequestDTO = {
  email: string;
  firstName: string;
  lastName: string;
  type: UserType;
  organizationIds?: string;
  phoneNumber?: string;
  isManualEntryEnabled?: boolean;
};

export type AdminUpdateUserResponseDTO =
  | {
    message: string;
    id: UUIDString;
    email: string;
  }
  | string;

export type ValidCredentialFormatResponseDTO = {
  email?: string | undefined;
  phoneNumber?: string | undefined;
  isValid: boolean;
  errorMessage?: string;
};

export type ValidCredentialFormatRequestDTO = {
  email?: string | undefined;
  phoneNumber?: string | undefined;
};

type ClientSummaryDTO2 = {
  totalNumberOfWorkouts: number;
  totalDurationOfWorkoutsInMs: number;
  longestStreakInDays: number;
  longestStreakDates?: [IsoDate, IsoDate];
  totalStepsCount: number;
  totalDistanceMeters: number;
  maxStepsInOneDay: number;
  maxStepsInOneDayDateTime?: RawTimestamp;
  currentStreakInDays: number;
  lastLoginDateTime?: RawTimestamp;
  currentStreakDates?: [IsoDate, IsoDate];
  lastWorkoutDateTime?: RawTimestamp;
  lastWorkoutName?: string;
};

export type ClientAppUserDTO2 = BaseAppUser & ClientSummaryDTO2;

export type TrainerClientSummaryResponseDTO2 = {
  clients: ClientAppUserDTO2[];
  totalNumberOfWorkouts: number;
  totalDurationOfWorkoutsInMs: number;
};

export type SearchAppUserRequestDTO = {
  searchQuery?: string | undefined;
};

export type SearchAppUserResponseDTO = BaseAppUser[] | undefined;

export type FitbitSettings = {
  accessToken?: string;
  userId?: string;
};

type StreakMetadata = {
  streakStartIsoDate: IsoDate; // Date the streak began
  lastCompletedIsoDate: IsoDate; // Most recent movement date
  longestStreakStartIsoDate: IsoDate; // Date the longest streak began
  longestStreakEndIsoDate: IsoDate; // Date the longest streak ended
};

type StreakMetadataDay<T extends StreakMetadata> = Partial<T> & {
  isInCurrentStreak: boolean;
  currentStreakInDays: number;
  isContinueStreak: boolean;
  isCompletedToday: boolean;
};

export type MovementStreakMetadata = StreakMetadata;

export type MovementStreakMetadataDay = StreakMetadataDay<MovementStreakMetadata>;

export type QuizStreakMetadata = StreakMetadata & {
  totalQuizzesCompleted: number; // Total number of quizzes completed for user
};

export type QuizStreakMetadataDay = StreakMetadataDay<QuizStreakMetadata>;

export type PushNotificationSettings = {
  settings?: Partial<Record<PushNotificationTypes, PushNotificationConfig>>;
  expoPushToken?: string | undefined;
  isNotificationEnabled: boolean;
  blogCategoriesDisabled?: WellnessBlogCategories[];
};

export enum UserGoalTypes {
  DAILY_GOAL_STEP_COUNT = 'dailyGoalStepCount',
  DAILY_GOAL_MILEAGE_IN_METERS = 'dailyGoalMileageInMeters',
}

export type UserGoal = {
  goalType: UserGoalTypes;
  goalValue: number;
  startDate: IsoDate; // When the goal starts
  endDate?: IsoDate; // Optional: When the goal ends; null/undefined means "active indefinitely"
};

export const isFitbitSettings = (value: unknown): value is FitbitSettings =>
  !!value &&
  typeof value === 'object' &&
  'accessToken' in value &&
  typeof (value as {accessToken?: string}).accessToken === 'string' &&
  'userId' in value &&
  typeof (value as {userId?: string}).userId === 'string';

export type BaseAppUser = {
  id: UUIDString;
  firstName: string;
  lastName: string;
  email: string;
  type: UserType;
  phoneNumber?: string | undefined;
  profilePicture?: ImageUrl | undefined;
  organizationIds?: UUIDString[] | undefined;
};

export const BASE_APP_USER_FIELDS = [
  'id',
  'firstName',
  'lastName',
  'email',
  'type',
  'phoneNumber',
  'profilePicture',
  'organizationIds',
] as const;

export type AppUser = BaseAppUser & {
  lastLoginDateTime?: RawTimestamp;
  lastModifiedDateTime?: RawTimestamp;
  accountCreatedDateTime: RawTimestamp;
  /**
   * @deprecated use `appUser/healthData` collection documents instead
   */
  healthDayStats: HealthStats[];
  /**
   * @deprecated use `appUser/healthData` collection documents instead
   */
  weightSamples?: WeightSample[];
  lastWeightSamplesSync?: RawTimestamp;
  lastHealthStatsSync?: RawTimestamp;
  isBackgroundHealthSyncEnabled?: true;
  hasAcceptedPrivacyPolicy?: true;
  /**
   * If enabled, the use distance metric from health apps, and DON'T derive distance from steps
   */
  isMileageGpsSourced?: true;
  /**
   * If enabled, the user can manually enter health stats
   */
  isManualEntryEnabled?: boolean | undefined;
  stepLength?: number;
  heightInInches?: number;
  /**
   * @deprecated use `goals` instead
   */
  dailyGoalStepCount?: number;
  /**
   * @deprecated use `goals` instead
   */
  dailyGoalMileageInMeters?: number;
  /**
   * @deprecated use `pushNotificationSettings.expoPushToken` instead
   */
  expoPushToken?: string;
  /**
   * @deprecated use `pushNotificationSettings` instead
   */
  pushNotificationTypesDisabled?: PushNotificationTypes[];
  pushNotificationSettings?: PushNotificationSettings;
  organizationIds?: string[];
  timeZone?: TimeZones;
  nativeAppVersion?: `${number}.${number}.${number}`;
  analytics?:
    | {
      deviceInfo?: DeviceInfoType | undefined;
    }
    | undefined;
  fitbit?: FitbitSettings;
  /**
   * @deprecated Use `trackingDeviceType` instead
   */
  wearableDeviceBrand?: TrackingDeviceTypes;
  trackingDeviceType?: TrackingDeviceTypes;
  trackingApp?: string;
  quizStreak?: QuizStreakMetadata;
  movementStreak?: MovementStreakMetadata;
  goals?: UserGoal[] | undefined;
};

export type AppUserHealthData = Pick<
  AppUser,
  | 'id'
  | 'healthDayStats'
  | 'weightSamples'
  | 'stepLength'
  | 'isMileageGpsSourced'
  | 'fitbit'
  | 'accountCreatedDateTime'
  | 'timeZone'
>;

export type EditAppUser = Pick<
  AppUser,
  | 'id'
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'phoneNumber'
  | 'goals'
  | 'profilePicture'
  | 'isMileageGpsSourced'
  | 'heightInInches'
  | 'dailyGoalStepCount'
  | 'dailyGoalMileageInMeters'
  | 'stepLength'
  | 'accountCreatedDateTime'
  | 'timeZone'
>;

export type SignUpAppUserRequestDTO = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  inviteCodeMetadata?: ChallengeJoinInviteCode | undefined;
};

export const transformAppUserToBaseUser = ({
  email,
  firstName,
  id,
  lastName,
  organizationIds,
  phoneNumber,
  profilePicture,
  type,
}: BaseAppUser): BaseAppUser => ({
  id,
  firstName,
  lastName,
  email,
  type,
  ...(phoneNumber && {phoneNumber}),
  ...(profilePicture && {profilePicture}),
  ...(organizationIds && organizationIds.length > 0 && {organizationIds}),
});

export const transformAppUserToAdminUpdateUserDTO = (
  appUser: AppUser | undefined,
): AdminUpdateUserRequestDTO | undefined => {
  if (!appUser) return;
  const {email, firstName, isManualEntryEnabled, lastName, organizationIds, phoneNumber, type} =
    appUser;
  return {
    email,
    firstName,
    lastName,
    type,
    ...(organizationIds && {organizationIds: organizationIds.join(',')}),
    ...(phoneNumber && {phoneNumber}),
    ...(isManualEntryEnabled && {isManualEntryEnabled}),
  };
};

export const isAppUserClient = (appUser: BaseAppUser) =>
  appUser.type === UserType.TEACHER ||
  appUser.type === UserType.STUDENT ||
  appUser.type === UserType.CLIENT;

export const isAppUserTrainer = (appUser: BaseAppUser) =>
  appUser.type === UserType.TRAINER ||
  appUser.type === UserType.COACH ||
  appUser.type === UserType.ADMIN;

const SPECIAL_ADMIN_USER_EMAILS = new Set([
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
]);
export const isAppUserAdmin = (appUser: BaseAppUser) =>
  appUser.type === UserType.ADMIN || SPECIAL_ADMIN_USER_EMAILS.has(appUser.email);

const FRIENDLY_APP_USER_TYPE_LABELS = {
  [UserType.TEACHER]: 'Teacher',
  [UserType.TRAINER]: 'Trainer',
  [UserType.COACH]: 'Coach',
  [UserType.STUDENT]: 'Student',
  [UserType.CLIENT]: 'Client',
  [UserType.ADMIN]: 'Admin',
};

export const getAppUserTypeLabel = (type: UserType) => FRIENDLY_APP_USER_TYPE_LABELS[type];
