import type {XOR} from '../utils';
import type {RawTimestamp} from '../firebase';
import type {UUIDString} from './shared';

export type InviteCode = `F-${string}`;

export const isValidInviteCode = (value: unknown): value is InviteCode =>
  !!value && typeof value === 'string' && value.startsWith('F-');

export enum InviteCodeActionType {
  JOIN_CHALLENGE = 'JOIN_CHALLENGE',
  OTHER = 'OTHER',
}

export type InviteCodeDocument = {
  /**
   * auto-generated short token (same as `code`)
   */
  id: InviteCode;
  /**
   * auto-generated short token (same as `id`)
   */
  code: InviteCode;
  actionType: InviteCodeActionType;
  payload: Record<string, unknown>;
  createdBy: UUIDString;
  createdAt: RawTimestamp;
  expiresAt?: RawTimestamp; // optional TTL
  usageCount: number; // for analytics or one-time tokens
};

/**
 * Payload type for JOIN_CHALLENGE action
 */
export type InviteCodeChallengePayload = {
  challengeId: UUIDString;
  teamId: UUIDString | null;
  groupId: UUIDString | null;
  isPublicInvite?: boolean;
};

/**
 * Payload type for OTHER action
 */
type InviteCodeOtherPayload = Record<string, unknown>;

/**
 * Maps each action type to its corresponding payload type
 */
type InviteCodePayloadMap = {
  [InviteCodeActionType.JOIN_CHALLENGE]: InviteCodeChallengePayload;
  [InviteCodeActionType.OTHER]: InviteCodeOtherPayload;
};

/**
 * Type-safe function to get the payload for a specific invite code action type
 *
 * @param inviteCode The invite code document
 * @param actionType The action type to get the payload for
 * @returns The strongly typed payload for the given action type
 */
export const getInviteCodePayload = <T extends InviteCodeActionType>(
  inviteCode: InviteCodeDocument,
  actionType: T,
): InviteCodePayloadMap[T] | undefined =>
  inviteCode.actionType === actionType
    ? (inviteCode.payload as InviteCodePayloadMap[T])
    : undefined;

export enum ChallengeJoinOperation {
  /** Always create a new group and make the user a captain */
  CREATE = 'CREATE',
  /** Join an existing public group if available, or create a new one */
  ASSIGN = 'ASSIGN',
}

/**
 * For joining a challenge via an invite code
 */
export type ChallengeJoinInviteCode = {
  inviteCode: InviteCode;
  teamId?: UUIDString | undefined;
  groupId?: UUIDString | undefined;
  parentGroupId?: UUIDString | undefined;
  joinOperation?: ChallengeJoinOperation | undefined;
};

/**
 * For joining a challenge directly, without an invite code
 */
export type ChallengeJoinDirect = {
  challengeId: UUIDString;
  teamId?: UUIDString | undefined;
  groupId?: UUIDString | undefined;
};

export const isChallengeJoinInviteCode = (
  payload: ChallengeJoinPayload,
): payload is ChallengeJoinInviteCode => 'inviteCode' in payload;

export const isChallengeJoinDirect = (
  payload: ChallengeJoinPayload,
): payload is ChallengeJoinDirect => 'challengeId' in payload;

export type ChallengeJoinPayload = XOR<ChallengeJoinInviteCode, ChallengeJoinDirect>;

export type GetInviteCodeRequestDTO = {
  id: InviteCode;
};
