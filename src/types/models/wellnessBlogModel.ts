import type {DateDTO, IsoDateDTO} from './shared';

export enum WellnessBlogCategories {
  EXERCISE = 'exercise',
  HYDRATION = 'hydration',
  NUTRITION = 'nutrition',
  SLEEP = 'sleep',
  STRESS_MANAGEMENT = 'stressManagement',
  CIRCADIAN_RHYTHM = 'circadianRhythm',
  SUNLIGHT = 'sunlight',
  HUMAN_BIOLOGY = 'humanBiology',
}

export const ALL_WELLNESS_BLOG_CATEGORIES = [
  WellnessBlogCategories.EXERCISE,
  WellnessBlogCategories.HYDRATION,
  WellnessBlogCategories.NUTRITION,
  WellnessBlogCategories.SLEEP,
  WellnessBlogCategories.STRESS_MANAGEMENT,
  WellnessBlogCategories.CIRCADIAN_RHYTHM,
  WellnessBlogCategories.SUNLIGHT,
  WellnessBlogCategories.HUMAN_BIOLOGY,
] as const;

export const isWellnessBlogCategory = (type: unknown): type is WellnessBlogCategories =>
  ALL_WELLNESS_BLOG_CATEGORIES.includes(type as WellnessBlogCategories);

export const isWellnessBlogCategories = (types: unknown): types is WellnessBlogCategories[] =>
  !!types && Array.isArray(types) && types.every(isWellnessBlogCategory);

export type WellnessBlog = {
  id: string;
  title: string;
  body: string;
  type: WellnessBlogCategories;
  url?: string;
  createdDateTime?: string;
  lastModifiedDateTime?: string;
  order: number;
};

export type WellnessBlogRequestDTO = (DateDTO | IsoDateDTO) & {
  disabledCategories?: WellnessBlogCategories[] | undefined;
};
