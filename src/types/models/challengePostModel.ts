import type {BasePost, UUIDString} from './shared';

export type ChallengePostBase = BasePost & {
  challengeId: UUIDString;
};

export type ChallengePostTeams = ChallengePostBase & {
  /**
   *  If defined, specific to a team feed
   *  If null, it's in the global challenge feed
   * */
  teamId: UUIDString | null;
};

export const isChallengePostTeams = (value: unknown): value is ChallengePostTeams =>
  !!value &&
  typeof value === 'object' &&
  'teamId' in value &&
  (value as {teamId?: string}).teamId !== undefined;

export type ChallengePostGroups = ChallengePostBase & {
  /**
   *  If defined, specific to a group feed
   *  If null, it's in the global challenge feed
   */
  groupId: UUIDString | null;
};

export const isChallengePostGroups = (value: unknown): value is ChallengePostGroups =>
  !!value &&
  typeof value === 'object' &&
  'groupId' in value &&
  (value as {groupId?: string}).groupId !== undefined;

export type TrainerMessagePostTeams = ChallengePostBase & {
  teamIds: (UUIDString | null)[];
};

export type TrainerMessagePostGroups = ChallengePostBase & {
  groupIds: (UUIDString | null)[];
};

export const isTrainerMessagePostTeams = (value: unknown): value is TrainerMessagePostTeams =>
  !!value &&
  typeof value === 'object' &&
  'teamIds' in value &&
  Array.isArray((value as {teamIds?: string[]}).teamIds);

export const isTrainerMessagePostGroups = (value: unknown): value is TrainerMessagePostGroups =>
  !!value &&
  typeof value === 'object' &&
  'groupIds' in value &&
  Array.isArray((value as {groupIds?: string[]}).groupIds);
