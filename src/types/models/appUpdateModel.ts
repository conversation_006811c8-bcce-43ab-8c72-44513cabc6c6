import type {RawTimestamp} from '../firebase';

export type AppVersion = `${number}.${number}.${number}`;

export const isAppVersionType = (version: unknown): version is AppVersion =>
  typeof version === 'string' && /^\d+\.\d+\.\d+$/.test(version);

export type AppUpdateDocument = {
  platform: 'ios' | 'android';
  lastModifiedDateTime: RawTimestamp;
  currentBannerMetadata: UpdateMetadata;
};

type UpdateMetadata = {
  minimumVersion: AppVersion;
  startDate: RawTimestamp;
};
