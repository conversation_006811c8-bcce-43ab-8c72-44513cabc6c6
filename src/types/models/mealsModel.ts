import type {RawTimestamp} from '../firebase';
import type {ImageUrl, UUIDString} from './shared';

export type MealImage = {
  imageUrl: ImageUrl;
  caption?: string | undefined;
};

export type Meal = {
  name: string;
  notes: string;
  creatorId: UUIDString;
  userId: UUIDString;
  images?: MealImage[];
  type: MealTypes;
  isCompleted?: true;
  // metadata fields
  id: UUIDString;
  createdDateTime: RawTimestamp;
  dateTime: RawTimestamp;
  copiedMealId?: UUIDString;
};

export type CreateMeal = Meal & {
  clientIds?: UUIDString[];
};

export enum MealTypes {
  BREAKFAST = 'BREAKFAST',
  LUNCH = 'LUNCH',
  DINNER = 'DINNER',
  SNACK = 'SNACK',
}

const MEAL_TYPE_FRIENDLY_MAP = {
  [MealTypes.BREAKFAST]: 'Breakfast',
  [MealTypes.LUNCH]: 'Lunch',
  [MealTypes.DINNER]: 'Dinner',
  [MealTypes.SNACK]: 'Snack',
};

export const getMealTypeFriendlyName = (mealType: MealTypes) => MEAL_TYPE_FRIENDLY_MAP[mealType];
