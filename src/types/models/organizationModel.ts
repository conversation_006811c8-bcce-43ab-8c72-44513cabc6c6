import type {RawTimestamp} from '../firebase';
import type {UUIDString} from './shared';

export enum OrganizationType {
  GYM = 'GYM',
  TEAM = 'TEAM',
  ADMIN = 'ADMIN',
}

export const allOrganizationTypes = [
  OrganizationType.GYM,
  OrganizationType.TEAM,
  OrganizationType.ADMIN,
] as const;

const FRIENDLY_ORGANIZATION_TYPES = {
  [OrganizationType.GYM]: 'Gym',
  [OrganizationType.TEAM]: 'Team',
  [OrganizationType.ADMIN]: 'Admin',
};

export const getOrganizationTypeLabel = (type: OrganizationType): string =>
  FRIENDLY_ORGANIZATION_TYPES[type];

const ORGANIZATION_TYPE_ICONS = {
  [OrganizationType.GYM]: 'dumbbell',
  [OrganizationType.TEAM]: 'account-group',
  [OrganizationType.ADMIN]: 'shield-account',
};

export const getOrganizationTypeIcon = (type: OrganizationType) => ORGANIZATION_TYPE_ICONS[type];

export type Organization = {
  id: UUIDString;
  name: string;
  type: OrganizationType;
  createdDateTime: RawTimestamp;
  lastModifiedDateTime: RawTimestamp;
  adminIds: UUIDString[];
  coachIds: UUIDString[];
  clientIds: UUIDString[];
  parentOrganizationId?: UUIDString;
  childOrganizationIds?: UUIDString[];
  isHideChallenges?: true;
};

export enum OrganizationRole {
  ADMIN = 'ADMIN',
  COACH = 'COACH',
  CLIENT = 'CLIENT',
}

export const allOrganizationRoles = [
  OrganizationRole.CLIENT,
  OrganizationRole.COACH,
  OrganizationRole.ADMIN,
] as const;

const FRIENDLY_ORGANIZATION_ROLES = {
  [OrganizationRole.ADMIN]: 'Admin',
  [OrganizationRole.COACH]: 'Coach',
  [OrganizationRole.CLIENT]: 'Client',
};

export const getOrganizationRoleLabel = (role: OrganizationRole): string =>
  FRIENDLY_ORGANIZATION_ROLES[role];

export type OrganizationAssignState = {
  organizationId: string;
  organizationRole: OrganizationRole;
};
