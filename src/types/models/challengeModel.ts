import type {OneOf} from '../utils';
import type {GroupsChallenge} from './challengeGroupsModel';
import type {IndividualChallenge, TeamsChallenge} from './challengeTeamsModel';
import type {UUIDString} from './shared';
import {ChallengeGroupingType} from './challengeBase';

export type Challenge = OneOf<[IndividualChallenge, TeamsChallenge, GroupsChallenge]>;

export type ParticipantsChallenge = OneOf<[IndividualChallenge, TeamsChallenge]>;

export const CHALLENGE_PARTICIPANTS_GROUPING_TYPES = [
  ChallengeGroupingType.INDIVIDUAL,
  ChallengeGroupingType.TEAMS,
];

const isIndividualChallengeGroupingType = (
  type: ChallengeGroupingType,
): type is ChallengeGroupingType.INDIVIDUAL => type === ChallengeGroupingType.INDIVIDUAL;

const isTeamsChallengeGroupingType = (
  type: ChallengeGroupingType,
): type is ChallengeGroupingType.TEAMS => type === ChallengeGroupingType.TEAMS;

export const isParticipantsChallengeGroupingType = (
  type: ChallengeGroupingType,
): type is ChallengeGroupingType.TEAMS | ChallengeGroupingType.INDIVIDUAL =>
  isTeamsChallengeGroupingType(type) || isIndividualChallengeGroupingType(type);

export const isGroupsChallengeGroupingType = (
  type: ChallengeGroupingType,
): type is ChallengeGroupingType.GROUPS => type === ChallengeGroupingType.GROUPS;

export const isIndividualChallenge = (challenge: Challenge): challenge is IndividualChallenge =>
  isIndividualChallengeGroupingType(challenge.groupingType);

export const isTeamsChallenge = (challenge: Challenge): challenge is TeamsChallenge =>
  isTeamsChallengeGroupingType(challenge.groupingType);

export const isParticipantsChallenge = (challenge: Challenge): challenge is ParticipantsChallenge =>
  isTeamsChallenge(challenge) || isIndividualChallenge(challenge);

export const isGroupsChallenge = (challenge: Challenge): challenge is GroupsChallenge =>
  isGroupsChallengeGroupingType(challenge.groupingType);

export type GetChallengeRequestDTO = {
  id: UUIDString;
};

export type GetChallengeGroupRequestDTO = {
  challengeId: UUIDString;
  groupId: UUIDString;
};

export type GetChallengeGroupParticipantCountRequestDTO = {
  challengeId: UUIDString;
  groupId: UUIDString;
};

export type GetChallengeGroupParticipantCountResponseDTO = {
  count: number;
};

export type GetGroupsChallengeParentGroupsRequestDTO = {
  challengeId: UUIDString;
};
