import {Path, Svg} from 'react-native-svg';
import type {SvgProps} from '@types';
import {memoComponent} from '@utils';

export const MealIcon: React.FC<SvgProps> = memoComponent(({fill = '#000', scale = 1}) => {
  const height = 32.490_002;
  const width = 19.27;

  return (
    <Svg
      fill={fill}
      height={height * scale}
      preserveAspectRatio='xMidYMid meet'
      viewBox={`0 0 ${width} ${height}`}
      width={width * scale}
    >
      <Path d='M18.91 1.7c0-.77-.26-1.41-.87-1.59v8.03c0 .27-.23.49-.52.49-.29 0-.52-.22-.52-.49V.74c0-.4-.19-.72-.42-.72-.23 0-.42.32-.42.72V8.14c0 .27-.22.48-.49.49h-.05a.504.504 0 0 1-.49-.49V.74c0-.4-.19-.72-.42-.72-.23 0-.42.32-.42.72v7.4c0 .27-.23.49-.52.49-.29 0-.52-.22-.52-.49V.11c-.61.19-.87.83-.87 1.59l-.36 8.41c0 .92.73 1.66 1.63 1.66h1.34l-.9 18.43v.12c0 1.18.68 2.13 1.53 2.17h.05c.85-.04 1.53-.99 1.53-2.17v-.12l-.9-18.43h1.34c.9 0 1.63-.74 1.63-1.66l-.36-8.41h-.01Z' />
      <Path d='M8.47 6.23C8.47 3.17 6.57 0 4.24 0 1.91 0 0 3.17 0 6.23c0 2.76 1.55 5.06 3.57 5.47l-.9 18.5v.12c0 1.2.7 2.17 1.57 2.17.87 0 1.57-.97 1.57-2.17v-.09L4.9 11.71C6.92 11.29 8.47 9 8.47 6.24Z' />
    </Svg>
  );
});
