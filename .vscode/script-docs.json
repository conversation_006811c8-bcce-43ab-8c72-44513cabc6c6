{"install:all": "Install dependencies for all packages in the monorepo including `functions`, `report-generation`, `swagger-docs`, and `web-sign-up`", "dev": "Start dev environment with `Expo` app and `Firebase` emulators running concurrently", "dev:firebase": "Build functions and start `Firebase` emulators for local dev", "serve": "Start `Firebase` functions emulator in serve mode", "start:base": "Start `Expo` dev server with optimized settings and custom port `8081`", "prestart": "Set up local dev environment variables before starting the app", "start": "Start the `Expo` dev server in dev mode", "start:dev": "Alias for start command to run dev server", "start:debug": "Start `Expo` dev server in debug mode without tunnel", "start:tunnel": "Start `Expo` dev server with tunnel enabled for external access", "prestart:prod": "Set up local prod environment variables before starting the app", "start:prod": "Start the `Expo` dev server with prod environment configuration", "android:base": "Run the app on Android device/emulator using `Expo CLI`", "android": "Build and run the app on Android in dev mode", "android:dev": "Build and run the app on Android device/emulator in dev environment", "android:dev:device": "Build and run the app on specific Android device (`N156DL`) in dev mode", "android:prod": "Build and run the app on Android device/emulator in prod environment", "android:prod:device": "Build and run the app on specific Android device (`N156DL`) in prod mode", "ios:base": "Run the app on iOS device/simulator using `Expo CLI`", "ios": "Build and run the app on iOS in dev mode", "ios:dev": "Build and run the app on iOS device/simulator in dev environment", "ios:dev:device": "Build and run the app on specific iOS device in dev mode", "ios:dev:release": "Build and run the app on iOS simulator in dev environment with release configuration", "ios:dev:release:device": "Build and run the app on specific iOS device in dev environment with release configuration", "ios:prod": "Build and run the app on iOS device/simulator in prod environment", "ios:prod:device": "Build and run the app on specific iOS device in prod mode", "ios:prod:release": "Build and run the app on specific iOS device in prod environment with release configuration", "prelint": "Run pre-lint setup for `report-generation` package before main linting", "lint": "Run `ESLint` with auto-fix on the entire codebase with increased memory allocation", "prelint:ci": "Build `report-generation` package before running CI linting", "lint:ci": "Run `ESLint` in CI mode across all packages with concurrent execution", "lint:ci:1": "Run `ESLint` on `src` directory with zero warnings tolerance for CI", "lint:ci:2": "Run `ESLint` on `functions` directory with zero warnings tolerance for CI", "lint:ci:3": "Run `ESLint` on `web-sign-up` directory with zero warnings tolerance for CI", "lint:ci:4": "Run `ESLint` on all JavaScript/TypeScript files with zero warnings tolerance for CI", "lint:ci-all": "Run `ESLint` on entire codebase with zero warnings tolerance for CI", "lint:debug": "Run `ESLint` with timing information and debug output for performance analysis", "knip": "Run `Knip` to find unused files, dependencies, and exports with auto-fix", "typecheck": "Run `TypeScript` compiler to check types across the entire project", "typecheck:ci": "Run `TypeScript` compiler in CI mode without incremental compilation", "typecheck:debug": "Run `TypeScript` compiler with detailed diagnostics and file explanations", "test:frontend": "Run `Jest` tests for frontend code using `jest.config.cjs` configuration", "test:backend": "Run `Jest` tests for backend functions in the `functions` directory", "test": "Run `Jest` tests with default configuration", "test:deprecated": "Run both frontend and backend tests concurrently (deprecated approach)", "test:watch": "Run `Jest` tests in watch mode for continuous testing during dev", "test:coverage": "Run `Jest` tests with coverage report for `src/utils` directory", "test:only": "Run specific test file in watch mode for focused testing", "test:ci": "Run complete CI test suite including doctor, typecheck, lint, and tests", "import:users": "Import users from JSON file to dev environment using custom script", "import:generate-mock": "Generate mock data for testing and dev purposes", "import:import-mock": "Import generated mock data into the dev environment", "cloc": "Count lines of code excluding `node_modules` and build directories", "ci:sync": "Sync `Firestore` indexes from dev to local `firestore.indexes.json` file", "ci:sync:deploy": "Deploy `Firestore` indexes from local file to prod environment", "ci-cd:local": "Run local CI/CD pipeline for testing deployment processes", "ci-cd:local:update": "Run local CI/CD update process for testing updates", "ci-cd:local:rollback": "Rollback the last `EAS` update deployment", "env:local:prod": "Decrypt and set up prod environment variables locally", "env:local:dev": "Decrypt and set up dev environment variables locally", "env:ci:prod": "Decrypt prod environment variables for CI environment", "env:ci:dev": "Decrypt dev environment variables for CI environment", "env:local-cli:dev": "Display command to manually set dev environment key", "env:local-cli:prod": "Display command to manually set prod environment key", "build:ci:dev:android": "Build Android app in dev profile using `EAS Build` in CI", "build:ci:dev:ios": "Build iOS app in dev profile using `EAS Build` in CI", "build:ci:preview": "Build iOS app in preview profile locally using `EAS Build`", "build:ci:prod:android": "Build Android app in prod profile using `EAS Build` in CI", "build:ci:prod:ios": "Build iOS app in prod profile using `EAS Build` in CI", "build:ci:prod:android:submit": "Build and auto-submit Android app to `Play Store` using `EAS Build`", "build:ci:prod:ios:submit": "Build and auto-submit iOS app to `App Store` using `EAS Build`", "submit:local:prod:ios": "Submit latest iOS build to `App Store` from local environment", "build:local:dev:ios": "Build iOS app in dev profile locally using `EAS Build`", "build:local:dev:android": "Build Android app in dev profile locally using `EAS Build`", "build:local:prod:ios": "Build iOS app in prod profile locally using `EAS Build`", "build:local:prod:android": "Build Android app in prod profile locally using `EAS Build`", "eas-build-post-install": "Run post-install script during `EAS Build` process", "deploy:dev:link": "Deploy link hosting and optimized linking function to dev environment", "deploy:prod:link": "Deploy link hosting and optimized linking function to prod environment", "deploy:local:dev": "Deploy all `Firebase` services to dev environment from local machine", "deploy:local:dev:only": "Deploy specific `Firebase` functions to dev environment", "deploy:local:prod": "Deploy all `Firebase` services to prod environment from local machine", "deploy:local:prod:only": "Deploy specific `Firebase` functions to prod environment", "deploy:ci:dev:content": "Update app configuration and wellness content in dev environment", "deploy:ci:prod:content": "Update app configuration and wellness content in prod environment", "deploy:ci:dev": "Deploy all `Firebase` services and content to dev environment in CI", "deploy:ci:prod": "Deploy all `Firebase` services and content to prod environment in CI", "deploy:ota-update:dev": "Deploy over-the-air update to dev channel using `EAS Update`", "deploy:ota-update:prod": "Deploy over-the-air update to prod channel using `EAS Update`", "build:inspect:prod": "Inspect prod iOS build archive and output to local directory", "submit:production": "Submit latest prod iOS build to `App Store`", "submit:android": "Submit latest prod Android build to `Play Store`", "submit:ios:local": "Submit specific iOS build file to `App Store` from local machine", "submit:android:local": "Submit specific Android build file to `Play Store` from local machine", "import": "Display reference to import runbook documentation", "backup": "Display reference to `GCP` Disaster Recovery documentation", "export:dev": "Export user data from dev environment to local files", "export:prod": "Export user data from prod environment to local files", "postinstall": "Apply patches to `node_modules` using `patch-package` after npm install", "prepare": "Set up `<PERSON><PERSON>` git hooks for the repository", "setup": "Run all setup scripts concurrently to configure dev environment", "setup:macos": "Install macOS dependencies using `Homebrew` and `Brewfile`", "setup:cli": "Install global CLI tools required for dev", "setup:dotenv": "Login to `dotenv-vault` and pull dev environment variables", "setup:eas": "Login to `Expo Application Services` (`EAS`) for builds and updates", "setup:firebase": "Login to `Firebase CLI` and set default project to dev", "setup:gh": "Login to `GitHub CLI` and save authentication token", "dotenv:pull:dev": "Pull dev environment variables from `dotenv-vault`", "dotenv:push:dev": "Push dev environment variables to `dotenv-vault`", "dotenv:pull:prod": "Pull prod environment variables from `dotenv-vault`", "dotenv:push:prod": "Push prod environment variables to `dotenv-vault`", "doctor": "Run `Expo Doctor` to check for common dev environment issues", "update-deps": "Update all dependencies to their latest versions and install them", "analyze": "Analyze iOS bundle size and dependencies using `React Native` bundle visualizer", "clean": "Clean build artifacts, caches, and temporary files using custom script", "release": "Run `semantic-release` in dry-run mode to preview version and changelog"}