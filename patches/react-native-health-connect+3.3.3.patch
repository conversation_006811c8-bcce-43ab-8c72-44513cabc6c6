diff --git a/node_modules/react-native-health-connect/android/src/main/java/dev/matinzd/healthconnect/utils/HealthConnectUtils.kt b/node_modules/react-native-health-connect/android/src/main/java/dev/matinzd/healthconnect/utils/HealthConnectUtils.kt
index b16609c..8345387 100644
--- a/node_modules/react-native-health-connect/android/src/main/java/dev/matinzd/healthconnect/utils/HealthConnectUtils.kt
+++ b/node_modules/react-native-health-connect/android/src/main/java/dev/matinzd/healthconnect/utils/HealthConnectUtils.kt
@@ -18,6 +18,9 @@ import java.time.ZoneOffset
 import java.time.Duration
 import java.time.Period
 import kotlin.reflect.KClass
+import java.time.LocalDateTime
+import java.time.ZoneId
+
 
 fun <T : Record> convertReactRequestOptionsFromJS(
   recordType: KClass<T>, options: ReadableMap
@@ -92,11 +95,13 @@ fun ReadableMap.getTimeRangeFilter(key: String? = null): TimeRangeFilter {
 
   val operator = timeRangeFilter.getString("operator")
 
-  val startTime =
-    if (timeRangeFilter.hasKey("startTime")) Instant.parse(timeRangeFilter.getString("startTime")) else null
+  val startTime = if (timeRangeFilter.hasKey("startTime")) {
+    LocalDateTime.ofInstant(Instant.parse(timeRangeFilter.getString("startTime")), ZoneId.systemDefault())
+} else null
 
-  val endTime =
-    if (timeRangeFilter.hasKey("endTime")) Instant.parse(timeRangeFilter.getString("endTime")) else null
+val endTime = if (timeRangeFilter.hasKey("endTime")) {
+    LocalDateTime.ofInstant(Instant.parse(timeRangeFilter.getString("endTime")), ZoneId.systemDefault())
+} else null
 
   when (operator) {
     "between" -> {
