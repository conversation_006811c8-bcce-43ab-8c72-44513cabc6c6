import {ERROR_COLOR} from '../theme';

type Option = {
  label: string;
  value: string;
};

type ValidationStatus = {
  errorMessage?: string;
  isPending?: boolean;
  isValid?: boolean;
};

type SelectFormFieldProps = {
  hasInteracted?: boolean;
  hintText?: string;
  id: string;
  isRequired?: boolean;
  label: string;
  onChange: (value: string) => void;
  options: Option[];
  pendingText?: string;
  validation?: ValidationStatus;
  value: string;
};

/**
 * A reusable select form field component that can optionally include validation
 */
export const SelectFormField: React.FC<SelectFormFieldProps> = ({
  hasInteracted,
  hintText,
  id,
  isRequired = false,
  label,
  onChange,
  options,
  pendingText,
  validation,
  value,
}) => {
  // Check if we should render with validation
  const hasValidation = !!validation;

  // Extract validation properties if they exist
  const errorMessage = validation?.errorMessage;
  const isPending = validation?.isPending;
  const isValid = validation?.isValid;

  return (
    <div style={{marginBottom: 24, textAlign: 'left'}}>
      <label
        htmlFor={id}
        style={{
          display: hasValidation ? 'flex' : 'block',
          marginBottom: 8,
          fontWeight: 500,
          color: '#333',
          minHeight: 22,
          ...(hasValidation ? {justifyContent: 'space-between'} : {}),
        }}
      >
        <span>
          {label}
          {isRequired && <span style={{color: ERROR_COLOR}}>*</span>}
        </span>
        {hasValidation && isValid && <span>✅</span>}
      </label>
      <select
        aria-describedby={hasValidation ? `${id}-hint` : undefined}
        aria-label={`Select ${label}`}
        aria-required={isRequired ? 'true' : 'false'}
        id={id}
        required={isRequired}
        style={{
          width: '100%',
          padding: '12px 16px',
          fontSize: 16,
          borderRadius: 8,
          transition: 'border-color 0.2s',
          border:
            hasValidation && hasInteracted && !isValid
              ? `1px solid ${ERROR_COLOR}`
              : '1px solid #ddd',
          backgroundColor: 'white',
          appearance: 'none',
          backgroundImage:
            'url("data:image/svg+xml;charset=UTF-8,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'currentColor\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'><polyline points=\'6 9 12 15 18 9\'></polyline></svg>")',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'right 12px center',
          backgroundSize: 16,
        }}
        value={value}
        onChange={e => onChange(e.target.value)}
      >
        {options.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {hasValidation && (
        <span
          id={`${id}-hint`}
          style={{
            display: 'block',
            marginTop: 6,
            fontSize: 14,
            ...(hasInteracted && !isValid ? {color: ERROR_COLOR} : {color: '#666'}),
          }}
        >
          {(() => {
            if (!hasInteracted) return hintText;
            if (isPending) return pendingText;
            if (errorMessage) return errorMessage;

            return '‎'; // Empty space character to maintain height
          })()}
        </span>
      )}
    </div>
  );
};
