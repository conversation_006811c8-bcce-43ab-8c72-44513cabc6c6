import {ERROR_COLOR} from '../theme';

type ValidationStatus = {
  errorMessage?: string | undefined;
  isDisableCheck?: boolean | undefined;
  isPending?: boolean | undefined;
  isValid?: boolean | undefined;
};

type FormFieldProps = {
  autoComplete?: string;
  disableMargin?: boolean;
  hasInteracted?: boolean;
  hintText?: string;
  iconRight?: React.ReactNode;
  id: string;
  isRequired?: boolean | undefined;
  label: string;
  labelRight?: React.ReactNode;
  onChange: (value: string) => void;
  pattern?: string;
  pendingText?: string;
  placeholder: string;
  type?: string;
  validation?: ValidationStatus | undefined;
  value: string;
};

/**
 * A reusable form field component that can optionally include validation
 */
// eslint-disable-next-line max-lines-per-function, complexity -- handles options from props
export const FormField: React.FC<FormFieldProps> = ({
  autoComplete,
  disableMargin,
  hasInteracted = true,
  hintText,
  iconRight,
  id,
  isRequired = true,
  label,
  labelRight,
  onChange,
  pattern,
  pendingText,
  placeholder,
  type = 'text',
  validation,
  value,
}) => {
  // Check if we should render with validation
  const hasValidation = !!validation;

  // Extract validation properties if they exist
  const errorMessage = validation?.errorMessage;
  const isPending = validation?.isPending;
  const isValid = validation?.isValid;
  const isDisableCheck = validation?.isDisableCheck;

  // Adjust '40px' based on the expected size of your icon + desired spacing
  const inputPaddingRight = iconRight ? 40 : 16;

  // Generate unique IDs for accessibility
  const hintId = `${id}-hint`;
  const errorId = errorMessage ? `${id}-error` : undefined;
  const ariaDescribedBy =
    [hasValidation ? hintId : null, errorMessage && hasInteracted && !isValid ? errorId : null]
      .filter(Boolean)
      .join(' ') || undefined;

  // Determine validation state for ARIA
  const hasError = hasInteracted && !isValid && !isPending && !!errorMessage;

  return (
    <div style={{marginBottom: disableMargin ? 0 : 24, textAlign: 'left'}}>
      <div style={{display: 'flex', alignItems: 'center', marginBottom: 4}}>
        <label
          htmlFor={id}
          id={`${id}-label`}
          style={{
            display: 'flex',
            fontWeight: 500,
            color: '#333',
            ...(hasValidation ? {justifyContent: 'space-between', width: '100%'} : {}),
          }}
        >
          <div style={{display: 'flex', alignItems: 'center', marginBottom: 4}}>
            <span style={{paddingRight: 4}}>
              {label}
              {isRequired && (
                <span aria-hidden='true' style={{color: ERROR_COLOR}}>
                  *
                </span>
              )}
            </span>
            {labelRight}
          </div>
          {hasValidation && isValid && !isDisableCheck && (
            <span aria-hidden='true' aria-label='Valid' role='img'>
              ✅
            </span>
          )}
        </label>
      </div>

      <div style={{position: 'relative'}}>
        <input
          aria-describedby={ariaDescribedBy}
          aria-invalid={hasError ? 'true' : 'false'}
          aria-required={isRequired ? 'true' : 'false'}
          autoComplete={autoComplete}
          id={id}
          pattern={pattern}
          placeholder={placeholder}
          required={isRequired}
          style={{
            width: '100%',
            padding: '12px 16px',
            paddingRight: inputPaddingRight,
            fontSize: 16,
            borderRadius: 8,
            transition: 'border-color 0.2s',
            border:
              hasValidation && hasInteracted && !isValid
                ? `1px solid ${ERROR_COLOR}`
                : '1px solid #ddd',
          }}
          type={type}
          value={value}
          onChange={e => onChange(e.target.value)}
        />

        {iconRight && (
          <div
            style={{
              position: 'absolute',
              right: 12,
              top: '50%',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
            }}
          >
            {iconRight}
          </div>
        )}
      </div>

      {hasValidation && (
        <div
          id={hintId}
          role={hasError ? 'alert' : 'status'}
          style={{
            display: 'block',
            marginTop: 6,
            fontSize: 14,
            ...(hasInteracted && !isValid && !isPending ? {color: ERROR_COLOR} : {color: '#666'}),
          }}
        >
          {(() => {
            if (!hasInteracted) return hintText;
            if (isPending) return pendingText;
            if (errorMessage) return errorMessage;

            return '‎'; // Empty space character to maintain height
          })()}
        </div>
      )}
    </div>
  );
};
