import {DownloadFlyFitButtons} from '../DownloadFlyFitButtons';
import {SUCCESS_COLOR} from '../theme';

export const SuccessMessage: React.FC = () => (
  <div
    aria-labelledby='successTitle'
    id='newUserPanel'
    role='region'
    style={{
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      borderRadius: 8,
      marginTop: 24,
    }}
  >
    <h2 id='successTitle' style={{color: SUCCESS_COLOR, marginBottom: 16}}>
      Your FlyFit Account is Created!
      <span aria-label='Celebration' role='img'>
        {' 🥳'}
      </span>
    </h2>
    <p style={{textAlign: 'left'}}>
      We sent you a welcome email to download FlyFit, or you can download FlyFit now with the
      buttons below and login with your email.
    </p>

    <DownloadFlyFitButtons />
  </div>
);
