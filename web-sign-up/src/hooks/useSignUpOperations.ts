import {useMutation, useQuery} from '@tanstack/react-query';
import {handleSignUpOperation} from '../config/api';
import {
  type InviteCode,
  type SignUpAppUserRequestDTO,
  SignUpOperations,
  type UUIDString,
} from '../types';
import {cleanEmail, cleanPhoneNumber, isValidEmailTest, isValidPhoneTest} from '../utils';

const MS_ONE_MINUTE = 60_000;

export const useIsValidEmail = (rawEmail: string | undefined) => {
  const cleanedEmail = cleanEmail(rawEmail ?? '');
  return useQuery({
    queryKey: ['isValidEmail', cleanedEmail],
    queryFn: () => {
      if (!cleanedEmail) {
        return {
          isValid: false,
          errorMessage: 'Email is required',
        };
      }

      const isValidFormat = isValidEmailTest(cleanedEmail);
      if (!isValidFormat) {
        return {
          isValid: false,
          errorMessage: 'Email must be a valid email address',
        };
      }

      return handleSignUpOperation(SignUpOperations.isValidNewUserCredentials)({
        email: cleanedEmail,
      });
    },
    enabled: !!cleanedEmail,
    staleTime: MS_ONE_MINUTE,
  });
};

export const useIsValidPhone = (rawPhone: string | undefined) => {
  const cleanedPhone = cleanPhoneNumber(rawPhone ?? '');
  return useQuery({
    queryKey: ['isValidPhone', cleanedPhone],
    queryFn: () => {
      if (!cleanedPhone) {
        return {
          isValid: false,
          errorMessage: 'Phone number is required',
        };
      }

      const isValidFormat = isValidPhoneTest(cleanedPhone);
      if (!isValidFormat) {
        return {
          isValid: false,
          errorMessage: 'Phone number must be 10 digits',
        };
      }

      return handleSignUpOperation(SignUpOperations.isValidNewUserCredentials)({
        phoneNumber: cleanedPhone,
      });
    },
    enabled: !!cleanedPhone,
    staleTime: MS_ONE_MINUTE,
  });
};

export const useSignUpMutation = () =>
  useMutation<void, Error, SignUpAppUserRequestDTO>({
    mutationKey: ['useSignUpMutation'],
    mutationFn: async (data: SignUpAppUserRequestDTO) => {
      // await new Promise(resolve => setTimeout(resolve, 1_000_000));
      // throw new Error('Failed to create user');

      const isSuccess = await handleSignUpOperation(SignUpOperations.signUpAppUser)(data);

      if (isSuccess) return;
      throw new Error('Failed to create user');
    },
  });

export const useChallengeById = (challengeId: UUIDString | undefined) =>
  useQuery({
    queryKey: ['challenge', challengeId],
    queryFn: async () => {
      if (!challengeId) return;
      return handleSignUpOperation(SignUpOperations.getChallengeById)({id: challengeId});
    },
    enabled: !!challengeId,
    staleTime: MS_ONE_MINUTE,
  });

export const useInviteCodeDocument = (inviteCode: InviteCode | undefined) =>
  useQuery({
    queryKey: ['inviteCode', inviteCode],
    queryFn: async () => {
      if (!inviteCode) return;
      return handleSignUpOperation(SignUpOperations.getInviteCodeDocumentById)({id: inviteCode});
    },
    enabled: !!inviteCode,
    staleTime: MS_ONE_MINUTE,
  });

export const useChallengeGroup = (
  challengeId: UUIDString | undefined,
  groupId: UUIDString | undefined,
) =>
  useQuery({
    queryKey: ['challengeGroup', challengeId, groupId],
    queryFn: async () => {
      if (!challengeId || !groupId) return;
      return handleSignUpOperation(SignUpOperations.getChallengeGroupById)({
        challengeId,
        groupId,
      });
    },
    enabled: !!challengeId && !!groupId,
    staleTime: MS_ONE_MINUTE,
  });

export const useChallengeGroupParticipantCount = (
  challengeId: UUIDString | undefined,
  groupId: UUIDString | undefined,
) =>
  useQuery({
    queryKey: ['challengeGroupParticipantCount', challengeId, groupId],
    queryFn: async () => {
      if (!challengeId || !groupId) return;
      const result = await handleSignUpOperation(
        SignUpOperations.getChallengeGroupParticipantCount,
      )({
        challengeId,
        groupId,
      });
      return result.count;
    },
    enabled: !!challengeId && !!groupId,
    staleTime: MS_ONE_MINUTE,
  });

export const useGroupsChallengeParentGroups = (challengeId: UUIDString | undefined) =>
  useQuery({
    queryKey: ['groupsChallengeParentGroups', challengeId],
    queryFn: async () => {
      if (!challengeId) return;
      return handleSignUpOperation(SignUpOperations.getGroupsChallengeParentGroups)({
        challengeId,
      });
    },
    enabled: !!challengeId,
    staleTime: MS_ONE_MINUTE,
  });
