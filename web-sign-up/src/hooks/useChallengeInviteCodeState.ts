import {useEffect} from 'react';
import {match as patternMatch} from 'ts-pattern';
import {
  type InviteCodeChallengePayload,
  isGroupsChallenge,
  isIndividualChallenge,
  isParticipantsChallenge,
  isTeamsChallenge,
} from '../types';
import {
  type InviteCodeChallengeStatus,
  useNewUserForm,
  useSetIsInviteCodeMetadataValid,
} from './atoms';
import {
  useChallengeById,
  useChallengeGroup,
  useChallengeGroupParticipantCount,
  useGroupsChallengeParentGroups,
} from './useSignUpOperations';
import {useValueIsUndefinedDelay} from './useValueIsUndefinedDelay';

// eslint-disable-next-line complexity -- handling the various states
export const useChallengeInviteCodeState = (payload: InviteCodeChallengePayload) => {
  const {data: challengeInternal} = useChallengeById(payload.challengeId);

  const {
    isUndefinedAfterDelay,
    value: challenge,
  } = useValueIsUndefinedDelay(challengeInternal, 20_000);

  const {data: group} = useChallengeGroup(challenge?.id, payload.groupId ?? undefined);
  const groupsChallenge = challenge && isGroupsChallenge(challenge) ? challenge : undefined;
  const {data: groupSize} = useChallengeGroupParticipantCount(
    challenge?.id,
    payload.groupId ?? undefined,
  );
  const {data: parentGroups} = useGroupsChallengeParentGroups(groupsChallenge && challenge?.id);
  const participantsChallenge =
    challenge && isParticipantsChallenge(challenge) ? challenge : undefined;
  const team = participantsChallenge?.teams?.find(t => t.id === payload.teamId);
  const teamSize = participantsChallenge?.participants.filter(p =>
    payload.teamId ? p.teamId === payload.teamId : false,
  ).length;

  const currentTeamSize =
    challenge &&
    patternMatch(challenge)
      .when(isParticipantsChallenge, () => teamSize)
      .when(isGroupsChallenge, () => groupSize)
      .exhaustive();
  // Only show full if challenge has loaded, there is a max team size, and there is a team or group
  // selected for this invite
  const isTeamOrGroupFull =
    challenge &&
    currentTeamSize !== undefined &&
    challenge.maxTeamSize !== undefined &&
    (!!team || !!group)
      ? currentTeamSize >= challenge.maxTeamSize
      : undefined;

  const teamOrGroupName = challenge
    ? patternMatch(challenge)
        .when(isParticipantsChallenge, () => team?.name)
        .when(isGroupsChallenge, () => group?.name)
        .exhaustive()
    : undefined;

  const isLoading =
    !challenge ||
    (!!groupsChallenge && parentGroups === undefined) ||
    // (!!groupsChallenge && (!!selectedGroupId || parentGroups === undefined)) ||
    (!!groupsChallenge && !!payload.groupId && currentTeamSize === undefined) ||
    (!!participantsChallenge && currentTeamSize === undefined);

  // Determine the current status based on component state - NOTE: order of checking boolean matters
  const status: InviteCodeChallengeStatus = (() => {
    if (isLoading) {
      return isUndefinedAfterDelay ? 'NOT_FOUND' : 'LOADING';
    }

    if (isTeamOrGroupFull) {
      return 'TEAM_FULL';
    }

    return 'AWAITING_RESPONSE';
  })();

  const setIsInviteCodeMetadataValid = useSetIsInviteCodeMetadataValid();
  const newUserForm = useNewUserForm();

  useEffect(() => {
    const isValidIndividual = !!challenge && isIndividualChallenge(challenge);
    const isValidTeams = !!challenge && isTeamsChallenge(challenge) &&
      (!!newUserForm.inviteCodeMetadata?.teamId || !!newUserForm.inviteCodeMetadata?.joinOperation);
    const isValidGroupsChallenge = !!challenge && isGroupsChallenge(challenge);
    const isValidGroupJoinPublic = !!newUserForm.inviteCodeMetadata?.parentGroupId &&
      !!newUserForm.inviteCodeMetadata.joinOperation;
    const isValidGroupJoinSpecific = !!newUserForm.inviteCodeMetadata?.groupId;
    const isValidGroups = isValidGroupsChallenge &&
      (isValidGroupJoinPublic || isValidGroupJoinSpecific);
    setIsInviteCodeMetadataValid(
      status === 'AWAITING_RESPONSE' && (isValidIndividual || isValidTeams || isValidGroups),
    );
  }, [
    challenge,
    newUserForm.inviteCodeMetadata?.groupId,
    newUserForm.inviteCodeMetadata?.joinOperation,
    newUserForm.inviteCodeMetadata?.parentGroupId,
    newUserForm.inviteCodeMetadata?.teamId,
    setIsInviteCodeMetadataValid,
    status,
  ]);

  return {
    status,
    challenge,
    groupsChallenge,
    parentGroups,
    teamOrGroupName,
  };
};
