import {
  handleGetChallengeGroupById,
  handleGetChallengeGroupParticipantCount,
  handleGetGroupsChallengeParentGroups,
  handleIsValidNewUserCredentials,
  handleSignUpAppUserRequest,
} from '../../../endpointHelpers';
import {LOGGER, onFirebaseRequest} from '../../../firebase';
import {getChallengeById, getInviteCodeById} from '../../../firestore';
import {patternMatch, SignUpOperations, StatusCodes, whenSignUpOperation} from '../../../types';
import {
  composeMiddleware,
  handleGetDocumentById,
  withCors,
  withValidPostRequest,
} from '../../../utils';

export const signUpOperation = onFirebaseRequest(
  composeMiddleware(
    'signUpOperation',
    async (request, response) => {
      await patternMatch(request.body)
        .when(whenSignUpOperation(SignUpOperations.isValidNewUserCredentials), async body => {
          await handleIsValidNewUserCredentials(body, response);
        })
        .when(whenSignUpOperation(SignUpOperations.getChallengeById), async body => {
          await handleGetDocumentById('Challenge', getChallengeById)(body.id, response);
        })
        .when(whenSignUpOperation(SignUpOperations.getInviteCodeDocumentById), async body => {
          await handleGetDocumentById('InviteCode', getInviteCodeById)(body.id, response);
        })
        .when(whenSignUpOperation(SignUpOperations.getChallengeGroupById), async body => {
          await handleGetChallengeGroupById(body, response);
        })
        .when(
          whenSignUpOperation(SignUpOperations.getChallengeGroupParticipantCount),
          async body => {
            await handleGetChallengeGroupParticipantCount(body, response);
          },
        )
        .when(whenSignUpOperation(SignUpOperations.getGroupsChallengeParentGroups), async body => {
          await handleGetGroupsChallengeParentGroups(body, response);
        })
        .when(whenSignUpOperation(SignUpOperations.signUpAppUser), async body => {
          await handleSignUpAppUserRequest(body, response);
        })
        .otherwise(body => {
          LOGGER.warn(
            `Invalid operation=${(body as Partial<{operation?: string | undefined}>).operation ?? 'undefined'}`,
          );
          response.status(StatusCodes.BAD_REQUEST_400).send('Invalid operation').end();
        });

      // Should already have ended response stream in the pattern match
    },
    withCors,
    withValidPostRequest,
  ),
);
