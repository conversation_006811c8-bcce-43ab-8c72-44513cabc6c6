import {handleSignUpAppUserRequest} from '../../../endpointHelpers';
import {onFirebaseRequest} from '../../../firebase';
import {type SignUpAppUserRequestDTO} from '../../../types';
import {composeMiddleware, withCors, withValidPostRequest} from '../../../utils';

export const signUpAppUser = onFirebaseRequest(
  {memory: '512MiB'},
  composeMiddleware(
    'signUpAppUser',
    (request, response) =>
      handleSignUpAppUserRequest(request.body as Partial<SignUpAppUserRequestDTO>, response),
    withCors,
    withValidPostRequest,
  ),
);
