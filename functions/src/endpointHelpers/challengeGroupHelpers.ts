import type {Response} from 'express';
import {LOGGER} from '../firebase';
import {
  getChallengeAllGroupForLevel,
  getChallengeGroupDocumentById,
  getChallengeGroupParticipantCountInternal,
  getGroupsChallengeById,
} from '../firestore';
import {
  type GetChallengeGroupParticipantCountRequestDTO,
  type GetChallengeGroupRequestDTO,
  type GetGroupsChallengeParentGroupsRequestDTO,
  StatusCodes,
} from '../types';
import {isDefinedString} from '../utils';

export const handleGetChallengeGroupById = async (
  data: Partial<GetChallengeGroupRequestDTO>,
  response: Response,
) => {
  if (!isDefinedString(data.challengeId) || !isDefinedString(data.groupId)) {
    LOGGER.warn(`challengeId and groupId are required, received ${JSON.stringify(data)}`);
    response.status(StatusCodes.BAD_REQUEST_400).send('challengeId and groupId are required').end();
    return;
  }

  const challengeGroup = await getChallengeGroupDocumentById(data.challengeId)(data.groupId);
  if (!challengeGroup) {
    LOGGER.warn(
      `No challenge group found for challengeId=${data.challengeId} and groupId=${data.groupId}`,
    );
    response.status(StatusCodes.BAD_REQUEST_400).send('No challenge group found').end();
    return;
  }

  LOGGER.debug(
    `Returning challenge group for challengeId=${data.challengeId} and groupId=${data.groupId}`,
  );
  response.status(StatusCodes.OK_200).send(challengeGroup).end();
};

export const handleGetChallengeGroupParticipantCount = async (
  data: Partial<GetChallengeGroupParticipantCountRequestDTO>,
  response: Response,
) => {
  if (!isDefinedString(data.challengeId) || !isDefinedString(data.groupId)) {
    LOGGER.warn(`challengeId and groupId are required, received ${JSON.stringify(data)}`);
    response.status(StatusCodes.BAD_REQUEST_400).send('challengeId and groupId are required').end();
    return;
  }

  const count = await getChallengeGroupParticipantCountInternal(data.challengeId, data.groupId);

  LOGGER.debug(
    `Returning challenge group participant count=${count} for challengeId=${data.challengeId} and groupId=${data.groupId}`,
  );

  response.status(StatusCodes.OK_200).send({count}).end();
};

export const handleGetGroupsChallengeParentGroups = async (
  data: Partial<GetGroupsChallengeParentGroupsRequestDTO>,
  response: Response,
) => {
  if (!isDefinedString(data.challengeId)) {
    LOGGER.warn(`challengeId is required, received ${JSON.stringify(data)}`);
    response.status(StatusCodes.BAD_REQUEST_400).send('challengeId is required').end();
    return;
  }

  const challenge = await getGroupsChallengeById(data.challengeId);
  if (!challenge) {
    LOGGER.warn(`No challenge found for id ${data.challengeId}`);
    response
      .status(StatusCodes.BAD_REQUEST_400)
      .send(`No challenge found for id ${data.challengeId}`)
      .end();
    return;
  }

  const parentOfLeafLevel = challenge.groupLevels.length - 1;
  const parentGroups = await getChallengeAllGroupForLevel(data.challengeId, parentOfLeafLevel);

  LOGGER.debug(`Returning ${parentGroups.length} parent groups for challenge ${data.challengeId}`);

  response.status(StatusCodes.OK_200).send(parentGroups).end();
};
