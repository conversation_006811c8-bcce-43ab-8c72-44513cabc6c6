import type {Response} from 'express';
import {
  StatusCodes,
  type ValidCredentialFormatRequestDTO,
  type ValidCredentialFormatResponseDTO,
} from '../types';
import {isDefinedString, isValidEmailTest, isValidPhoneTest} from '../utils';
import {doesUserExistEmail, doesUserExistPhoneNumber} from './createUserHelpers';

export const handleIsValidNewUserCredentials = async (
  {email, phoneNumber}: Partial<ValidCredentialFormatRequestDTO>,
  response: Response,
) => {
  if (!isDefinedString(email) && !isDefinedString(phoneNumber)) {
    const responseBody: ValidCredentialFormatResponseDTO = {
      isValid: false,
      errorMessage: 'Email or phone required',
    };
    response.status(StatusCodes.BAD_REQUEST_400).send(responseBody).end();
    return;
  }

  if (isDefinedString(email) && !isValidEmailTest(email)) {
    const responseBody: ValidCredentialFormatResponseDTO = {
      email,
      isValid: false,
      errorMessage: 'Invalid email format',
    };
    response.status(StatusCodes.OK_200).send(responseBody).end();
    return;
  }
  if (isDefinedString(phoneNumber) && !isValidPhoneTest(phoneNumber)) {
    const responseBody: ValidCredentialFormatResponseDTO = {
      phoneNumber,
      isValid: false,
      errorMessage: 'Invalid phone format',
    };
    response.status(StatusCodes.OK_200).send(responseBody).end();
    return;
  }

  const doesExistEmail = isDefinedString(email) && (await doesUserExistEmail(email));
  const doesExistPhone =
    isDefinedString(phoneNumber) && (await doesUserExistPhoneNumber(phoneNumber));

  if (doesExistEmail) {
    const responseBody: ValidCredentialFormatResponseDTO = {
      email,
      isValid: false,
      errorMessage: 'Email already taken',
    };
    response.status(StatusCodes.OK_200).send(responseBody).end();
    return;
  }
  if (doesExistPhone) {
    const responseBody: ValidCredentialFormatResponseDTO = {
      phoneNumber,
      isValid: false,
      errorMessage: 'Phone number already taken',
    };
    response.status(StatusCodes.OK_200).send(responseBody).end();
    return;
  }

  const responseBody: ValidCredentialFormatResponseDTO = {
    email,
    phoneNumber,
    isValid: true,
  };

  response.status(StatusCodes.OK_200).send(responseBody).end();
};
