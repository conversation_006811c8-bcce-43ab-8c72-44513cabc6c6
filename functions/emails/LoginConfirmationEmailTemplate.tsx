import {Container} from '@react-email/components';
import type {EmailDataMap, EmailTypes} from './types';
import {Button, EmailContentWrapper, Paragraph} from './Components';
import {makePreviewProps} from './helpers';

type LoginConfirmationEmailTemplateProps = EmailDataMap[EmailTypes.LOGIN_EMAIL];

export const LoginConfirmationEmailTemplate: React.FC<LoginConfirmationEmailTemplateProps> = ({
  firstName,
  loginLink,
}) => (
  <EmailContentWrapper previewText='Click the link to confirm your email and login to FlyFit'>
    <Paragraph>
      {firstName}, to access FlyFit, we need you to confirm your email to activate your account.
      Simply click the button below:
    </Paragraph>
    <Container className='flex flex-row items-center justify-center w-full'>
      <Button href={loginLink}>Confirm Email</Button>
    </Container>
    <Paragraph>Have questions or need a hand? Just reply to this email!</Paragraph>

    <Paragraph className='text-signatureGray'>The Fly Bodies Team</Paragraph>
  </EmailContentWrapper>
);

makePreviewProps(LoginConfirmationEmailTemplate, {
  firstName: 'Sean',
  email: '<EMAIL>',
  loginLink:
    'https://fly-fit-dev-link.web.app/?link=fly-fit://home-page/login-screen?email=<EMAIL>%26lang=en%26ltoken=********-f34d-436f-81e8-86574aacfda2',
});

export default LoginConfirmationEmailTemplate;
